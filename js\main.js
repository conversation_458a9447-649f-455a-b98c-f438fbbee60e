/**
 * Magic Menu - Main JavaScript File
 * Handles core application functionality, navigation, and UI interactions
 */

// Main application object
const MagicMenu = {
    // Initialize the application
    init() {
        this.setupEventListeners();
        this.initializeNavigation();
        this.initializeModals();
        this.initializeLazyLoading();
        this.initializeAnimations();
        console.log('Magic Menu initialized successfully');
    },

    // Set up global event listeners
    setupEventListeners() {
        // DOM content loaded
        document.addEventListener('DOMContentLoaded', () => {
            this.handleDOMReady();
        });

        // Window load
        window.addEventListener('load', () => {
            this.handleWindowLoad();
        });

        // Window resize
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));

        // Window scroll
        window.addEventListener('scroll', this.throttle(() => {
            this.handleScroll();
        }, 16));
    },

    // Handle DOM ready event
    handleDOMReady() {
        // Initialize error handling
        if (typeof ErrorHandler !== 'undefined') {
            ErrorHandler.init();
        }

        // Initialize data synchronization
        if (typeof DataSync !== 'undefined') {
            DataSync.init();
        }

        // Initialize cart display
        if (typeof Cart !== 'undefined') {
            Cart.updateCartDisplay();
        }

        // Initialize forms
        if (typeof Forms !== 'undefined') {
            Forms.init();
        }

        // Set active navigation item
        this.setActiveNavItem();
        
        // Setup data sync listeners
        this.setupDataSyncListeners();
        
        // Setup error recovery for forms
        this.setupFormErrorRecovery();
    },

    // Handle window load event
    handleWindowLoad() {
        // Hide loading spinner if present
        const loader = document.querySelector('.loader');
        if (loader) {
            loader.style.display = 'none';
        }

        // Initialize any components that need full page load
        this.initializeComponents();
    },

    // Handle window resize
    handleResize() {
        // Update mobile navigation
        this.updateMobileNavigation();
        
        // Update any size-dependent components
        this.updateComponentSizes();
    },

    // Handle window scroll
    handleScroll() {
        // Update navbar on scroll
        this.updateNavbarOnScroll();
        
        // Update scroll-to-top button
        this.updateScrollToTop();
    },

    // Initialize navigation functionality
    initializeNavigation() {
        const navbarToggle = document.querySelector('.navbar-toggle');
        const navbarNav = document.querySelector('.navbar-nav');

        if (navbarToggle && navbarNav) {
            // Store original hamburger icon
            const originalIcon = navbarToggle.textContent;

            // Add close button to mobile menu
            this.addCloseButtonToMobileMenu(navbarNav);

            navbarToggle.addEventListener('click', () => {
                const isOpen = navbarNav.classList.contains('show');

                if (isOpen) {
                    this.closeMobileMenu(navbarToggle, navbarNav, originalIcon);
                } else {
                    this.openMobileMenu(navbarToggle, navbarNav);
                }
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!navbarToggle.contains(e.target) && !navbarNav.contains(e.target)) {
                    this.closeMobileMenu(navbarToggle, navbarNav, originalIcon);
                }
            });

            // Close mobile menu when clicking on a link
            navbarNav.addEventListener('click', (e) => {
                if (e.target.tagName === 'A') {
                    this.closeMobileMenu(navbarToggle, navbarNav, originalIcon);
                }
            });

            // Handle escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && navbarNav.classList.contains('show')) {
                    this.closeMobileMenu(navbarToggle, navbarNav, originalIcon);
                    navbarToggle.focus(); // Return focus to toggle button
                }
            });
        }
    },

    // Add close button to mobile menu
    addCloseButtonToMobileMenu(navbarNav) {
        // Check if close button already exists
        if (navbarNav.querySelector('.close-menu')) return;

        const closeButton = document.createElement('button');
        closeButton.className = 'close-menu';
        closeButton.setAttribute('aria-label', 'Close menu');
        closeButton.textContent = '×';

        closeButton.addEventListener('click', () => {
            const navbarToggle = document.querySelector('.navbar-toggle');
            const originalIcon = '☰'; // Default hamburger icon
            this.closeMobileMenu(navbarToggle, navbarNav, originalIcon);
        });

        navbarNav.insertBefore(closeButton, navbarNav.firstChild);
    },

    // Open mobile menu with focus management
    openMobileMenu(navbarToggle, navbarNav) {
        navbarNav.classList.add('show');
        navbarToggle.setAttribute('aria-expanded', 'true');
        navbarToggle.textContent = '×'; // Change to close icon

        // Focus trap: focus first focusable element in menu
        const focusableElements = navbarNav.querySelectorAll(
            'button, a, input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        if (focusableElements.length > 0) {
            focusableElements[0].focus();
        }

        // Set up focus trap
        this.setupFocusTrap(navbarNav, focusableElements);
    },

    // Close mobile menu
    closeMobileMenu(navbarToggle, navbarNav, originalIcon) {
        navbarNav.classList.remove('show');
        navbarToggle.setAttribute('aria-expanded', 'false');
        navbarToggle.textContent = originalIcon; // Restore hamburger icon

        // Remove focus trap
        this.removeFocusTrap();
    },

    // Setup focus trap for mobile menu
    setupFocusTrap(container, focusableElements) {
        if (focusableElements.length === 0) return;

        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        this.focusTrapHandler = (e) => {
            if (e.key === 'Tab') {
                if (e.shiftKey) {
                    // Shift + Tab
                    if (document.activeElement === firstElement) {
                        e.preventDefault();
                        lastElement.focus();
                    }
                } else {
                    // Tab
                    if (document.activeElement === lastElement) {
                        e.preventDefault();
                        firstElement.focus();
                    }
                }
            }
        };

        document.addEventListener('keydown', this.focusTrapHandler);
    },

    // Remove focus trap
    removeFocusTrap() {
        if (this.focusTrapHandler) {
            document.removeEventListener('keydown', this.focusTrapHandler);
            this.focusTrapHandler = null;
        }
    },

    // Set active navigation item based on current page
    setActiveNavItem() {
        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        const navLinks = document.querySelectorAll('.navbar-nav a');

        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href === currentPage || (currentPage === '' && href === 'index.html')) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    },

    // Initialize modal functionality
    initializeModals() {
        const modalTriggers = document.querySelectorAll('[data-modal-target]');
        const modalCloses = document.querySelectorAll('[data-modal-close]');
        const modals = document.querySelectorAll('.modal');

        // Open modal
        modalTriggers.forEach(trigger => {
            trigger.addEventListener('click', (e) => {
                e.preventDefault();
                const targetModal = document.querySelector(trigger.dataset.modalTarget);
                if (targetModal) {
                    this.openModal(targetModal);
                }
            });
        });

        // Close modal
        modalCloses.forEach(closeBtn => {
            closeBtn.addEventListener('click', () => {
                const modal = closeBtn.closest('.modal');
                if (modal) {
                    this.closeModal(modal);
                }
            });
        });

        // Close modal when clicking outside
        modals.forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeModal(modal);
                }
            });
        });

        // Close modal with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    this.closeModal(openModal);
                }
            }
        });
    },

    // Open modal
    openModal(modal) {
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
        
        // Focus first focusable element
        const focusableElements = modal.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        if (focusableElements.length > 0) {
            focusableElements[0].focus();
        }
    },

    // Close modal
    closeModal(modal) {
        modal.classList.remove('show');
        document.body.style.overflow = '';
    },

    // Initialize lazy loading for images
    initializeLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        observer.unobserve(img);
                    }
                });
            });

            const lazyImages = document.querySelectorAll('img[data-src]');
            lazyImages.forEach(img => imageObserver.observe(img));
        }
    },

    // Initialize animations
    initializeAnimations() {
        // Smooth scroll for anchor links
        const anchorLinks = document.querySelectorAll('a[href^="#"]');
        anchorLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    },

    // Update navbar appearance on scroll
    updateNavbarOnScroll() {
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        }
    },

    // Update scroll-to-top button
    updateScrollToTop() {
        const scrollToTopBtn = document.querySelector('.scroll-to-top');
        if (scrollToTopBtn) {
            if (window.scrollY > 300) {
                scrollToTopBtn.classList.add('show');
            } else {
                scrollToTopBtn.classList.remove('show');
            }
        }
    },

    // Update mobile navigation
    updateMobileNavigation() {
        const navbarNav = document.querySelector('.navbar-nav');
        const navbarToggle = document.querySelector('.navbar-toggle');
        
        if (window.innerWidth >= 1024) {
            if (navbarNav) navbarNav.classList.remove('show');
            if (navbarToggle) navbarToggle.setAttribute('aria-expanded', 'false');
        }
    },

    // Update component sizes
    updateComponentSizes() {
        // Update any components that need size recalculation
        // This can be extended as needed
    },

    // Initialize components after page load
    initializeComponents() {
        // Initialize any components that need full page load
        // This can be extended as needed
    },

    // Utility function: Debounce
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Utility function: Throttle
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // Setup data sync listeners
    setupDataSyncListeners() {
        // Listen for data sync events
        window.addEventListener('dataSync', (event) => {
            this.handleDataSyncEvent(event.detail);
        });

        // Listen for storage events (fallback for older browsers)
        window.addEventListener('storage', (event) => {
            if (event.key && event.key.startsWith('sync_trigger_')) {
                try {
                    const syncData = JSON.parse(event.newValue);
                    this.handleDataSyncEvent({
                        key: syncData.key,
                        value: syncData.value,
                        timestamp: syncData.timestamp,
                        source: 'storage_trigger'
                    });
                } catch (error) {
                    console.error('Error processing sync trigger:', error);
                }
            }
        });
    },

    // Handle data sync events
    handleDataSyncEvent(detail) {
        const { key, value, source } = detail;
        
        // Update UI components based on synced data
        switch (key) {
            case 'user':
                this.handleUserDataSync(value, source);
                break;
            case 'userAddresses':
                this.handleAddressDataSync(value, source);
                break;
            case 'userPreferences':
                this.handlePreferencesDataSync(value, source);
                break;
            case 'lastOrder':
                this.handleOrderDataSync(value, source);
                break;
        }
        
        // Show sync notification if from another tab
        if (source !== 'manual_sync' && source !== 'storage_trigger') {
            this.showSyncNotification(key);
        }
    },

    // Handle user data synchronization
    handleUserDataSync(userData, source) {
        // Update cart display if user changed
        if (typeof Cart !== 'undefined') {
            Cart.updateCartDisplay();
        }
        
        // Refresh account page if currently viewing it
        if (window.location.pathname.includes('account.html')) {
            if (typeof AccountManager !== 'undefined' && AccountManager.checkAuthenticationStatus) {
                AccountManager.checkAuthenticationStatus();
            }
        }
    },

    // Handle address data synchronization
    handleAddressDataSync(addressData, source) {
        // Refresh account page if currently viewing it
        if (window.location.pathname.includes('account.html')) {
            if (typeof AccountManager !== 'undefined' && AccountManager.checkAuthenticationStatus) {
                AccountManager.checkAuthenticationStatus();
            }
        }
    },

    // Handle preferences data synchronization
    handlePreferencesDataSync(preferencesData, source) {
        // Refresh account page if currently viewing it
        if (window.location.pathname.includes('account.html')) {
            if (typeof AccountManager !== 'undefined' && AccountManager.checkAuthenticationStatus) {
                AccountManager.checkAuthenticationStatus();
            }
        }
    },

    // Handle order data synchronization
    handleOrderDataSync(orderData, source) {
        // Update order history if on account page
        if (window.location.pathname.includes('account.html')) {
            if (typeof AccountManager !== 'undefined' && AccountManager.loadRecentOrders) {
                AccountManager.loadRecentOrders();
            }
        }
    },

    // Show sync notification
    showSyncNotification(dataType) {
        const messages = {
            user: 'Profile updated in another tab',
            userAddresses: 'Addresses updated in another tab',
            userPreferences: 'Preferences updated in another tab',
            lastOrder: 'Order data updated in another tab'
        };
        
        const message = messages[dataType] || 'Data updated in another tab';
        this.showToast(message, 'info', 2000);
    },

    // Setup form error recovery
    setupFormErrorRecovery() {
        if (typeof ErrorHandler === 'undefined') return;

        // Check for preserved form data on page load
        document.addEventListener('DOMContentLoaded', () => {
            const forms = document.querySelectorAll('form[data-validate]');
            forms.forEach(form => {
                // Try to restore form data if available
                const restored = ErrorHandler.restoreUserInput(form);
                if (restored) {
                    // Add visual indicator that data was restored
                    const indicator = document.createElement('div');
                    indicator.className = 'alert alert-info form-restored-alert';
                    indicator.innerHTML = `
                        <span>📋 Form data restored from previous session</span>
                        <button type="button" class="btn btn-sm btn-secondary" onclick="this.parentNode.remove()">Dismiss</button>
                    `;
                    form.insertBefore(indicator, form.firstChild);
                    
                    // Auto-remove after 10 seconds
                    setTimeout(() => {
                        if (indicator.parentNode) {
                            indicator.remove();
                        }
                    }, 10000);
                }
            });
        });

        // Setup network status monitoring
        this.setupNetworkStatusMonitoring();
    },

    // Setup network status monitoring
    setupNetworkStatusMonitoring() {
        const updateNetworkStatus = () => {
            if (typeof ErrorHandler !== 'undefined') {
                if (navigator.onLine) {
                    ErrorHandler.hideOfflineIndicator();
                } else {
                    ErrorHandler.showOfflineIndicator();
                }
            }
        };

        // Initial check
        updateNetworkStatus();

        // Listen for network changes
        window.addEventListener('online', () => {
            updateNetworkStatus();
            this.showToast('Connection restored', 'success');
        });

        window.addEventListener('offline', () => {
            updateNetworkStatus();
            this.showToast('Connection lost. Working offline...', 'warning');
        });
    },

    // Show toast notification
    showToast(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} toast`;
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1060;
            min-width: 300px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;

        document.body.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 10);

        // Animate out and remove
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    }
};

// Initialize the application
MagicMenu.init();
