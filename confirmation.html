<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Order confirmation - Your Magic Menu order has been placed successfully. Track your delivery and enjoy authentic Nigerian cuisine.">
    <meta name="keywords" content="order confirmation, delivery tracking, Nigerian food order, Magic Menu">
    <title>Order Confirmation - Magic Menu | Your Order is Confirmed</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/icons/favicon.ico">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="styles/base.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/pages.css">
    <link rel="stylesheet" href="styles/responsive.css">
</head>
<body>
    <!-- Skip Link for Accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <!-- Navigation -->
    <nav class="navbar" role="navigation" aria-label="Main navigation">
        <div class="container">
            <div class="navbar-container">
                <a href="index.html" class="navbar-brand" aria-label="Magic Menu Home">
                    Magic Menu
                </a>
                
                <button class="navbar-toggle" type="button" aria-expanded="false" aria-controls="navbar-nav" aria-label="Toggle navigation menu">
                    ☰
                </button>
                
                <ul class="navbar-nav" id="navbar-nav">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="menu.html">Menu</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                    <li><a href="account.html">Account</a></li>
                    <li>
                        <a href="cart.html" class="cart-link" aria-label="Shopping cart with 0 items">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 22C9.55228 22 10 21.5523 10 21C10 20.4477 9.55228 20 9 20C8.44772 20 8 20.4477 8 21C8 21.5523 8.44772 22 9 22Z"/>
                                <path d="M20 22C20.5523 22 21 21.5523 21 21C21 20.4477 20.5523 20 20 20C19.4477 20 19 20.4477 19 21C19 21.5523 19.4477 22 20 22Z"/>
                                <path d="M1 1H5L7.68 14.39C7.77144 14.8504 8.02191 15.264 8.38755 15.5583C8.75318 15.8526 9.2107 16.009 9.68 16H19.4C19.8693 16.009 20.3268 15.8526 20.6925 15.5583C21.0581 15.264 21.3086 14.8504 21.4 14.39L23 6H6"/>
                            </svg>
                            Cart <span class="cart-count">0</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content">
        <!-- Confirmation Content -->
        <section class="confirmation-section section">
            <div class="container">
                <div class="confirmation-content">
                    <div class="confirmation-icon">✅</div>
                    <h1>Order Confirmed!</h1>
                    <p>Thank you for your order. We're preparing your delicious Nigerian dishes with care.</p>
                    
                    <div class="order-number" id="order-number">
                        Order #MM123456
                    </div>
                    
                    <div class="confirmation-details">
                        <!-- Order Details -->
                        <div class="confirmation-section">
                            <h3>Order Details</h3>
                            <div id="order-items-summary">
                                <!-- Order items will be loaded here -->
                            </div>
                        </div>
                        
                        <!-- Delivery Information -->
                        <div class="confirmation-section">
                            <h3>Delivery Information</h3>
                            <div id="delivery-info">
                                <!-- Delivery info will be loaded here -->
                            </div>
                        </div>
                        
                        <!-- Payment Information -->
                        <div class="confirmation-section">
                            <h3>Payment Summary</h3>
                            <div id="payment-summary">
                                <!-- Payment summary will be loaded here -->
                            </div>
                        </div>
                        
                        <!-- Estimated Delivery -->
                        <div class="confirmation-section">
                            <h3>Estimated Delivery Time</h3>
                            <div class="delivery-estimate">
                                <p><strong id="estimated-time">45 minutes</strong></p>
                                <p>We'll send you updates via SMS and email as your order progresses.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="confirmation-actions">
                        <a href="account.html" class="btn btn-primary btn-lg">Track Your Order</a>
                        <a href="menu.html" class="btn btn-secondary btn-lg">Order Again</a>
                        <a href="index.html" class="btn btn-secondary">Back to Home</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- What's Next Section -->
        <section class="whats-next section" style="background-color: var(--background-color);">
            <div class="container">
                <div class="section-header text-center">
                    <h2>What Happens Next?</h2>
                    <p>Here's what you can expect from your Magic Menu experience</p>
                </div>
                
                <div class="process-steps grid-3">
                    <div class="process-step">
                        <div class="step-icon">👨‍🍳</div>
                        <h3>We're Cooking</h3>
                        <p>Our chefs are preparing your order with fresh ingredients and authentic recipes.</p>
                    </div>
                    
                    <div class="process-step">
                        <div class="step-icon">🚚</div>
                        <h3>On the Way</h3>
                        <p>Your order will be carefully packed and dispatched by our delivery team.</p>
                    </div>
                    
                    <div class="process-step">
                        <div class="step-icon">🍽️</div>
                        <h3>Enjoy!</h3>
                        <p>Receive your hot, delicious meal and enjoy the authentic taste of Nigeria.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Support Section -->
        <section class="support-section section">
            <div class="container">
                <div class="support-content text-center">
                    <h2>Need Help?</h2>
                    <p>Our customer service team is here to assist you with any questions or concerns.</p>
                    
                    <div class="support-options grid-2">
                        <div class="support-option">
                            <div class="support-icon">📞</div>
                            <h3>Call Us</h3>
                            <p><a href="tel:+*************">+234 ************</a></p>
                            <p>Available 10 AM - 10 PM daily</p>
                        </div>
                        
                        <div class="support-option">
                            <div class="support-icon">💬</div>
                            <h3>WhatsApp</h3>
                            <p><a href="https://wa.me/*************">Chat with us</a></p>
                            <p>Quick responses, 24/7</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer" role="contentinfo">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Magic Menu</h3>
                    <p>Bringing authentic Nigerian cuisine to your doorstep with love, tradition, and the finest ingredients.</p>
                    <div class="social-links">
                        <a href="#" aria-label="Follow us on Facebook">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"/>
                            </svg>
                        </a>
                        <a href="#" aria-label="Follow us on Instagram">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect x="2" y="2" width="20" height="20" rx="5" ry="5"/>
                                <path d="m16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"/>
                                <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"/>
                            </svg>
                        </a>
                        <a href="#" aria-label="Follow us on Twitter">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"/>
                            </svg>
                        </a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="menu.html">Menu</a></li>
                        <li><a href="about.html">About Us</a></li>
                        <li><a href="contact.html">Contact</a></li>
                        <li><a href="faq.html">FAQ</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Customer Service</h4>
                    <ul>
                        <li><a href="account.html">My Account</a></li>
                        <li><a href="privacy.html">Privacy Policy</a></li>
                        <li><a href="terms.html">Terms of Service</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <address>
                        <p>📍 123 Victoria Island, Lagos, Nigeria</p>
                        <p>📞 <a href="tel:+*************">+234 ************</a></p>
                        <p>✉️ <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    </address>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Magic Menu. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="js/main.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/utils.js"></script>
    <script>
        // Load order confirmation details
        document.addEventListener('DOMContentLoaded', () => {
            loadOrderConfirmation();
        });

        function loadOrderConfirmation() {
            const lastOrder = Utils.storage.get('lastOrder');
            
            if (!lastOrder) {
                // No order found, redirect to menu
                MagicMenu.showToast('No order found. Redirecting to menu...', 'info');
                setTimeout(() => {
                    window.location.href = 'menu.html';
                }, 2000);
                return;
            }

            // Update order number
            document.getElementById('order-number').textContent = `Order #${lastOrder.orderNumber}`;

            // Load order items
            const orderItemsContainer = document.getElementById('order-items-summary');
            orderItemsContainer.innerHTML = lastOrder.items.map(item => `
                <div class="order-item">
                    <div class="order-item-details">
                        <h4>${item.name}</h4>
                        <div class="order-item-meta">Quantity: ${item.quantity}</div>
                    </div>
                    <div class="order-item-price">${Utils.formatCurrency(item.price * item.quantity)}</div>
                </div>
            `).join('');

            // Load delivery info
            const deliveryInfoContainer = document.getElementById('delivery-info');
            deliveryInfoContainer.innerHTML = `
                <p><strong>Name:</strong> ${lastOrder.customerInfo.firstName} ${lastOrder.customerInfo.lastName}</p>
                <p><strong>Phone:</strong> ${lastOrder.customerInfo.phone}</p>
                <p><strong>Address:</strong> ${lastOrder.customerInfo.address}, ${lastOrder.customerInfo.area}, ${lastOrder.customerInfo.city}</p>
                ${lastOrder.customerInfo.deliveryInstructions ? `<p><strong>Instructions:</strong> ${lastOrder.customerInfo.deliveryInstructions}</p>` : ''}
            `;

            // Load payment summary
            const paymentSummaryContainer = document.getElementById('payment-summary');
            paymentSummaryContainer.innerHTML = `
                <div class="summary-line">
                    <span>Subtotal:</span>
                    <span>${Utils.formatCurrency(lastOrder.totals.subtotal)}</span>
                </div>
                <div class="summary-line">
                    <span>VAT (7.5%):</span>
                    <span>${Utils.formatCurrency(lastOrder.totals.tax)}</span>
                </div>
                <div class="summary-line">
                    <span>Delivery Fee:</span>
                    <span>${Utils.formatCurrency(lastOrder.totals.deliveryFee)}</span>
                </div>
                <div class="summary-line total">
                    <span><strong>Total:</strong></span>
                    <span><strong>${Utils.formatCurrency(lastOrder.totals.total)}</strong></span>
                </div>
                <div class="payment-method-info">
                    <p><strong>Payment Method:</strong> ${getPaymentMethodText(lastOrder.customerInfo.paymentMethod)}</p>
                </div>
            `;

            // Update estimated delivery time
            const estimatedDelivery = new Date(lastOrder.estimatedDelivery);
            const now = new Date();
            const timeDiff = Math.ceil((estimatedDelivery - now) / (1000 * 60)); // minutes
            
            if (timeDiff > 0) {
                document.getElementById('estimated-time').textContent = `${timeDiff} minutes`;
            } else {
                document.getElementById('estimated-time').textContent = 'Any moment now!';
            }
        }

        function getPaymentMethodText(method) {
            switch (method) {
                case 'card': return 'Credit/Debit Card';
                case 'transfer': return 'Bank Transfer';
                case 'cash': return 'Cash on Delivery';
                default: return 'Unknown';
            }
        }
    </script>
</body>
</html>
